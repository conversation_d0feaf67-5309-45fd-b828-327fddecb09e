<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2" />
        <PackageReference Include="System.Text.Json" Version="8.0.5" />
        <PackageReference Include="MST.Common" Version="2.3.6" />
        <PackageReference Include="MST.Common.Azure" Version="2.5.2" />
        <PackageReference Include="MST.Common.AWS" Version="0.3.9-beta" />
        <PackageReference Include="MST.Common.MongoDB" Version="0.4.3-beta" />
        <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.2" />
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions" Version="8.0.17" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\NBA.NextGen.CMSPlatform.VODService.Infrastructure\NBA.NextGen.CMSPlatform.VODService.Infrastructure.csproj" />
        <!-- <ProjectReference Include="..\..\mst.common\src\MST.Common.MongoDB\MST.Common.MongoDB.csproj" /> -->
        <!-- <ProjectReference Include="..\..\mst.common\src\MST.Common.Azure\MST.Common.Azure.csproj" /> -->
    </ItemGroup>

</Project>
