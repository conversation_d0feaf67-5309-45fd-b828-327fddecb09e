using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MST.Common.Data;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
using Newtonsoft.Json;
using NBA.NextGen.CMSPlatform.VODService.Domain.CmsStatusNotifications;

namespace Vod.Common.Services;

public class EcmsService : IEcmsService
{
    private readonly ILogger logger;
    private readonly NormalFlowSettings settings;
    private readonly HttpClient httpClient;
    private readonly IVodAssetRegistryService vodAssetRegistryService;
    private readonly CmsStatusNotificationsSettings cmsStatusNotificationSettings;
    private readonly IObjectRepository<EcmsCacheRecord> cacheRepository;
    private readonly string? _wordpressApiKey;

    public EcmsService(IOptions<NormalFlowSettings> options, ILogger<EcmsService> logger, HttpClient httpClient, IVodAssetRegistryService vodAssetRegistryService, IOptions<CmsStatusNotificationsSettings> cmsStatusNotificationSettings, IObjectRepositoryFactory objectRepositoryFactory, IConfiguration configuration)
    {
        this.logger = logger;
        this.settings = options.Value;
        this.httpClient = httpClient;
        this.vodAssetRegistryService = vodAssetRegistryService;
        this.cmsStatusNotificationSettings = cmsStatusNotificationSettings.Value;
        this.cacheRepository = objectRepositoryFactory.Resolve<EcmsCacheRecord>();
        _wordpressApiKey = configuration["wordpress_api_key"];
    }

    private async Task<string> GetDataFromCacheAsync(string id, int retryCount = 0)
    {
        if (retryCount > 3)
        {
            return string.Empty;
        }

        var cache = await this.cacheRepository.GetItemAsync(id);
        var json = System.Text.Encoding.UTF8.GetString(System.Convert.FromBase64String(cache?.EncodedContent ?? ""));
        if (string.IsNullOrWhiteSpace(json))
        {
            logger.LogInformation($"ECMS Cache Returned empty for Id: {id} Attempt: {retryCount}");
            await Task.Delay(1000 * (retryCount + 1));
            return await GetDataFromCacheAsync(id, retryCount + 1);
        }

        return json;
    }

    public async Task<bool> SubmitToEcmsAsync(SubmitToEcmsWrapper wrapper)
    {
        try
        {
            logger.LogInformation("Loading ECMS Payload from Cache...");
            var json = await GetDataFromCacheAsync(wrapper.CacheId);

            if (string.IsNullOrWhiteSpace(json))
            {
                var msg = $"AssetId: {wrapper.ResourceId}, CacheId: {wrapper.CacheId}, Trigger: {wrapper.ContextPath} came back empty!!";
                await this.vodAssetRegistryService.AddAssetEventAsync(wrapper.ResourceId, AssetEventTypes.EcmsAssetCacheNotFound, DateTime.UtcNow, msg).ConfigureAwait(false);
                logger.LogError(msg);
                return true;
            }

            var ecmsAPI = this.settings.WordpressAPIDomain + wrapper.Endpoint;
            EnsureHttpClient();
            using var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
            this.logger.LogDebug("Preparing sending request to the ECMS endpoint {EcmsApi}", ecmsAPI);
            this.logger.LogDebug("Sending payload for file {WrapperFilePath} resourceId: {WrapperResourceId}", wrapper.FilePath, wrapper.ResourceId);
            this.logger.LogInformation("The payload to the ECMS is " + json);
            var result = await this.httpClient.PostAsync(new Uri(ecmsAPI), content).ConfigureAwait(false);

            var statusCode = (int)result.StatusCode;
            var response = await result.Content.ReadAsStringAsync();
            this.logger.LogInformation("The response from ECMS is " + response);

            if (statusCode >= 200 && statusCode < 300)
            {
                await this.vodAssetRegistryService.AddAssetEventAsync(wrapper.ResourceId, AssetEventTypes.SuccessfullySentToEcms, DateTime.UtcNow, response);
            }
            else
            {
                await this.vodAssetRegistryService.AddAssetEventAsync(wrapper.ResourceId, AssetEventTypes.FailedToSendEcms, DateTime.UtcNow, response);
            }

            result.EnsureSuccessStatusCode();

            Dictionary<string, object>? responseObject = JsonConvert.DeserializeObject<Dictionary<string, object>>(response);

            if (responseObject is not null && responseObject.ContainsKey("success"))
            {
                bool success = (bool)responseObject["success"];
                this.logger.LogInformation($"the status of {success}");

                if (success)
                {
                    await this.cacheRepository.DeleteItemAsync(wrapper.CacheId);
                    return true;
                }

                if (responseObject.TryGetValue("status", out var value))
                {
                    var status = value.ToString();

                    // If success is false, but the status is post_exists, this is actually ok.
                    if (status is not null && status.Equals("POST_EXISTS", StringComparison.OrdinalIgnoreCase))
                    {
                        await this.cacheRepository.DeleteItemAsync(wrapper.CacheId);
                        return true;
                    }
                }
            }

            var failureMessage = $"The ECMS did not indicate success or the response was invalid. ResourceId: {wrapper.ResourceId}";

            // At this point the API returned a response, but it was a failure or invalid.
            // We don't want to retry forever here, so lets return false,
            // and retry the entire message again.
            // This could be a valid case for dead lettering.
            await this.vodAssetRegistryService.AddAssetEventAsync(wrapper.ResourceId, AssetEventTypes.EcmsResponseIndicatesFailure, DateTime.UtcNow, failureMessage).ConfigureAwait(false);
            this.logger.LogError(failureMessage);
            await this.cacheRepository.DeleteItemAsync(wrapper.CacheId);
            return false;
        }
        catch (Exception e)
        {
            // An HTTP Exception, could the ecms be down? Throw, so we retry till it recovers.
            this.logger.LogError(e, "ECMS Request exception for " + wrapper.FilePath);
            await this.vodAssetRegistryService.AddAssetEventAsync(wrapper.ResourceId, AssetEventTypes.FailedToSendEcms, DateTime.UtcNow, e.Message).ConfigureAwait(false);
            throw;
        }
    }

    public async Task<bool> UpdateAssetInEcmsAsync(UpdateToEcmsWrapper request)
    {
        var identifier = request.StatusNotification.Identifier;
        var recordingStatus = request.StatusNotification.AdditionalInfo.RecordingStatus;
        var assetEvent = AssetEventTypes.VcmsProgressResponseReceived;
        string? publicMessage = null;

        if (request is null)
        {
            logger.LogWarning("Request came back null");
            return false;
        }

        if (request.StatusNotification.Operation == "distribution"
                    && request.StatusNotification.Status == StatusNotificationStatus.Succeeded)
            {
                // Here, internally we care about it being done.
                assetEvent = AssetEventTypes.VcmsCompletedResponseReceived;
            }

        if (request.StatusNotification.Status is StatusNotificationStatus.Failed)
        {
            assetEvent = AssetEventTypes.VcmsFailureResponseReceived;
            publicMessage = request?.StatusNotification?.FailureReason ?? "Could not find failure reason from MK";
        }

        logger.LogInformation($"ECMSService Adding Status Event: {identifier} - {assetEvent}");
        await this.vodAssetRegistryService.AddAssetEventAsync(identifier, assetEvent, System.DateTime.UtcNow, request.MediaKindMessage, publicMessage);

        var urlBuilder = new StringBuilder();
        urlBuilder.Append(this.cmsStatusNotificationSettings.Endpoint.TrimEnd('/')).Append("/private/api/v1/manage-nba-video/services/mediakind/webhook/program-update/");

        EnsureHttpClient();

        try
        {
            // Here we're proxying the source message to the ecms.
            using (var content = new StringContent(request.MediaKindMessage, Encoding.UTF8, "application/json"))
            {
                var uri = new Uri(urlBuilder.ToString());
                var result = await this.httpClient.PostAsync(uri, content);
                var statusCode = (int)result.StatusCode;
                var response = await result.Content.ReadAsStringAsync();
                if (statusCode >= 200 && statusCode < 300)
                {
                    await this.vodAssetRegistryService.AddAssetEventAsync(identifier, AssetEventTypes.SuccessfullySentVcmsResponseToEcms, DateTime.UtcNow, response);
                }
                else
                {
                    await this.vodAssetRegistryService.AddAssetEventAsync(identifier, AssetEventTypes.FailedToSendVcmsResponseToEcms, DateTime.UtcNow, response);
                }

                result.EnsureSuccessStatusCode();

            }
        }
        catch (Exception e)
        {
            await this.vodAssetRegistryService.AddAssetEventAsync(identifier, AssetEventTypes.FailedToSendVcmsResponseToEcms, System.DateTime.UtcNow, e.Message).ConfigureAwait(false);
            throw;
        }

        if (assetEvent == AssetEventTypes.VcmsCompletedResponseReceived)
        {
            await this.vodAssetRegistryService.AddAssetEventAsync(identifier, AssetEventTypes.WorkflowCompleted, System.DateTime.UtcNow).ConfigureAwait(false);
        }

        return true;
    }

    private void EnsureHttpClient()
    {
        if (this.httpClient is not null)
        {
            if (!this.httpClient.DefaultRequestHeaders.Contains("X-Api-Key"))
            {
                this.httpClient.DefaultRequestHeaders.TryAddWithoutValidation("X-Api-Key", _wordpressApiKey);
            }
        }
    }
}