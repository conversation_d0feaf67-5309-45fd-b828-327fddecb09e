using System;
using MongoDB.Bson.Serialization.Attributes;
using MST.Common.MongoDB;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using Newtonsoft.Json;

namespace Vod.IncomingAssetCopyProcessor;

[BsonIgnoreExtraElements]
public class AssetCopyRequest : ISupportLocking
{
    [JsonProperty("id")]
    public string Id { get; set; } = "";
    public string Source { get; set; } = "";
    public string Destination { get; set; } = "";
    public string Key { get; set; } = "";
    public IngestionFlowTypes FlowType { get; set; }
    public string BucketName { get; set; } = "";
    public double SizeInBytes { get; set; } = 0;
    public DateTime ArrivaleTimeUtc { get; set; } = DateTime.MaxValue;
    public string? OwnerId { get; set; }
    public string? Lock { get; set; }
}
