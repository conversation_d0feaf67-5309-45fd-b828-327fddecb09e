resource "aws_s3_bucket" "nf_s3_bucket" {
  bucket        = "mst-vod-${var.environment}-normalflow"
  force_destroy = false
  tags          = merge(local.tags, { Name = "mst-vod-${var.environment}-normalflow" })
}

resource "aws_s3_bucket_public_access_block" "nf_s3_bucket_public_access_block" {
  bucket                  = aws_s3_bucket.nf_s3_bucket.id
  block_public_acls       = true
  ignore_public_acls      = true
  block_public_policy     = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_notification" "nf_bucket_notification" {
  bucket      = aws_s3_bucket.nf_s3_bucket.id
  eventbridge = true
}

resource "aws_s3_bucket" "df_s3_bucket" {
  bucket        = "mst-vod-${var.environment}-directflow"
  force_destroy = false
  tags          = merge(local.tags, { Name = "mst-vod-${var.environment}-directflow" })
}

resource "aws_s3_bucket_public_access_block" "df_s3_bucket_public_access_block" {
  bucket                  = aws_s3_bucket.df_s3_bucket.id
  block_public_acls       = true
  ignore_public_acls      = true
  block_public_policy     = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_notification" "df_bucket_notification" {
  bucket      = aws_s3_bucket.df_s3_bucket.id
  eventbridge = true
}

resource "aws_s3_bucket" "df_generated_adi_s3_bucket" {
  bucket        = "mst-vod-${var.environment}-directflow-generated-adi"
  force_destroy = false
  tags          = merge(local.tags, { Name = "mst-vod-${var.environment}-directflow-generated-adi" })
}

resource "aws_s3_bucket_public_access_block" "df_generated_adi_s3_bucket_public_access_block" {
  bucket                  = aws_s3_bucket.df_generated_adi_s3_bucket.id
  block_public_acls       = true
  ignore_public_acls      = true
  block_public_policy     = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_notification" "df_generated_adi_bucket_notification" {
  bucket      = aws_s3_bucket.df_generated_adi_s3_bucket.id
  eventbridge = true
}

resource "aws_s3_bucket" "l2v_s3_bucket" {
  bucket        = "mst-vod-${var.environment}-livetovod"
  force_destroy = false
  tags          = merge(local.tags, { Name = "mst-vod-${var.environment}-livetovod" })
}

resource "aws_s3_bucket_public_access_block" "l2v_s3_bucket_public_access_block" {
  bucket                  = aws_s3_bucket.l2v_s3_bucket.id
  block_public_acls       = true
  ignore_public_acls      = true
  block_public_policy     = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_notification" "l2v_bucket_notification" {
  bucket      = aws_s3_bucket.l2v_s3_bucket.id
  eventbridge = true
}

resource "aws_s3_bucket" "output_s3_bucket" {
  bucket        = "mst-vod-${var.environment}-output"
  force_destroy = false
  tags          = merge(local.tags, { Name = "mst-vod-${var.environment}-output" })
}

resource "aws_s3_bucket_public_access_block" "output_s3_bucket_public_access_block" {
  bucket                  = aws_s3_bucket.output_s3_bucket.id
  block_public_acls       = true
  ignore_public_acls      = true
  block_public_policy     = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket" "transcribe_s3_bucket" {
  bucket        = "mst-vod-${var.environment}-transcribe"
  force_destroy = false
  tags          = merge(local.tags, { Name = "mst-vod-${var.environment}-transcribe" })
}

resource "aws_s3_bucket_public_access_block" "transcribe_s3_bucket_public_access_block" {
  bucket                  = aws_s3_bucket.transcribe_s3_bucket.id
  block_public_acls       = true
  ignore_public_acls      = true
  block_public_policy     = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket" "custom_s3_bucket" {
  bucket        = "mst-vod-${var.environment}-custom"
  force_destroy = false
  tags          = merge(local.tags, { Name = "mst-vod-${var.environment}-custom" })
}

resource "aws_s3_bucket_public_access_block" "custom_s3_bucket_public_access_block" {
  bucket                  = aws_s3_bucket.custom_s3_bucket.id
  block_public_acls       = true
  ignore_public_acls      = true
  block_public_policy     = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_policy" "nf_s3_bucket_policy" {
  bucket = aws_s3_bucket.nf_s3_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowExternalCompanyAccess"
        Effect = "Allow"
        Principal = {
          AWS = [
            var.mediakind_arn,
            var.mpd_hub_user_arn,
            var.mk_power_user_role_arn
          ]
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.nf_s3_bucket.arn,
          "${aws_s3_bucket.nf_s3_bucket.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_bucket_policy" "df_s3_bucket_policy" {
  bucket = aws_s3_bucket.df_s3_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowExternalCompanyAccess"
        Effect = "Allow"
        Principal = {
          AWS = [
            var.mediakind_arn,
            var.mpd_hub_user_arn,
            var.mk_power_user_role_arn
          ]

        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.df_s3_bucket.arn,
          "${aws_s3_bucket.df_s3_bucket.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_bucket_policy" "l2v_s3_bucket_policy" {
  bucket = aws_s3_bucket.l2v_s3_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowExternalCompanyAccess"
        Effect = "Allow"
        Principal = {
          AWS = [
            var.mediakind_arn,
            var.mpd_hub_user_arn,
            var.mk_power_user_role_arn
          ]
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.l2v_s3_bucket.arn,
          "${aws_s3_bucket.l2v_s3_bucket.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_bucket_policy" "output_s3_bucket_policy" {
  bucket = aws_s3_bucket.output_s3_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowExternalCompanyAccess"
        Effect = "Allow"
        Principal = {
          AWS = [
            var.mediakind_arn,
            var.mpd_hub_user_arn,
            var.mk_power_user_role_arn
          ]
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.output_s3_bucket.arn,
          "${aws_s3_bucket.output_s3_bucket.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_bucket_policy" "transcribe_s3_bucket_policy" {
  bucket = aws_s3_bucket.transcribe_s3_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowExternalCompanyAccess"
        Effect = "Allow"
        Principal = {
          AWS = [
            var.mediakind_arn,
            var.mpd_hub_user_arn,
            var.mk_power_user_role_arn
          ]
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.transcribe_s3_bucket.arn,
          "${aws_s3_bucket.transcribe_s3_bucket.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_bucket" "android_xr_s3_bucket" {
  bucket        = "mst-vod-${var.environment}-android-xr"
  force_destroy = false
  tags          = merge(local.tags, { Name = "mst-vod-${var.environment}-android-xr" })
}

resource "aws_s3_bucket_public_access_block" "android_xr_s3_bucket_public_access_block" {
  bucket                  = aws_s3_bucket.android_xr_s3_bucket.id
  block_public_acls       = true
  ignore_public_acls      = true
  block_public_policy     = true
  restrict_public_buckets = true
}