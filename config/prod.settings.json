{"AWS": {"Profile": "dev", "Region": "us-west-2"}, "CosmosSettings": {"DatabaseName": "ott-pdue2-vcms-cdb001"}, "MongoSettings": {"DatabaseName": "prod-vod-main"}, "SlackSettings": {"EnableOutput": true}, "CmsStatusNotificationsSettings": {"EnableEcmsNotification": "true", "EnableEventNotifier": "false", "Endpoint": "https://manage.nba.com", "EventGridEventType": "VodService.Status", "EventGridTopic": "vvaibhavEventGridTopic", "IgnoreCertificateValidation": "true", "MediaKindEnvKey": "proda"}, "QueueNames": {"IncomingBlob": "vcvodsv-production-incomingblob-cloudevents-queue", "SubmitToEcms": "vcvodsv-production-ecms-submit-queue", "SubmitToVcms": "vcvodsv-production-vcms-submit-queue", "UpdateEcms": "vcvodsv-production-ecms-update-queue", "AssetCopy": "vcvodsv-production-incomingblob-copy-queue"}, "AWSQueueNames": {"IncomingBlob": "https://sqs.us-west-2.amazonaws.com/************/vcvodsv-prod-incomingblob-cloudevents-queue.fifo", "SubmitToEcms": "https://sqs.us-west-2.amazonaws.com/************/vcvodsv-prod-ecms-submit-queue.fifo", "SubmitToVcms": "https://sqs.us-west-2.amazonaws.com/************/vcvodsv-prod-vcms-submit-queue.fifo", "UpdateEcms": "https://sqs.us-west-2.amazonaws.com/************/vcvodsv-prod-ecms-update-queue.fifo", "AssetCopy": "https://sqs.us-west-2.amazonaws.com/************/vcvodsv-prod-asset-copy-queue.fifo"}, "QueueSettings": {"MaximumBackOff": "30", "MaximumRetryCount": "3", "MinimumBackOff": "10", "Namespace": "ott-pdue2-message-sbn001", "Hostname": "ott-pdue2-message-sbn001.servicebus.windows.net", "TenantId": "e898ff4a-4b69-45ee-a3ae-1cd6f239feb2", "UseWebSockets": "true"}, "NormalFlowSettings": {"AccountName": "stmaminputmam1776", "BlobClientProviderSupport": "true", "ContainerName": "mst-vod-prod-normalflow", "CopyContainerName": "normal-flow", "CaptionContainer": "mst-vod-prod-normalflow", "EnableEventNotifier": "true", "EventGridEndpoint": "https://ott-pdue2-events-evg001.eastus2-1.eventgrid.azure.net/api/events", "EventGridTopic": "ott-pdue2-events-evg001", "LogLevel": "Debug", "MaxRetry": "2", "SleepSeconds": "180", "VodFeedbackContainer": "mst-vod-prod-livetovod", "VodFeedbackFolder": "VOD", "WordpressAPIDomain": "https://manage.nba.com/wp-json"}, "VideoIndexerSettings": {"ResourceGroup": "ott-pdue2-vcvodsv-rg001", "SubscriptionId": "96073bf6-fb80-40d4-b72f-785ec0a29c61", "AccountName": "ott-pdue2-vcvodsv-vidindexer001", "EnableChecker": true}, "FileTransformationSettings": {"GmsDomain": "https://gms.internal.nba.com", "GmsPortNumber": "8181", "GmsUser": "blobwatcher", "AccountName": "stmaminputmam1776", "ContainerName": "mst-vod-prod-directflow", "CopyContainerName": "direct-flow", "EnableMocking": "true", "VODServiceFunctionURL": "***********************************************/VODServiceFunction/VODServiceFunction", "WSCContainerPath": "wsc-condensed-json"}, "VcmsSettings": {"EnableEventNotifier": "false", "EventGridTopic": "ott-pdue2-events-evg001", "IngestContentUser": "fileupload", "ProviderId": "NBA", "SendWorkflowMsg": "true", "VodIngestionStartEventType": "VodService.VodIngestionStart", "Endpoint": "https://cms.proda.nba.tv3ce.com:8443", "AzureEndpoint": "https://************:8443", "IgnoreCertificateValidation": "true"}, "GamSettings": {"ApplicationName": "VOD Service App", "NetworkCode": "2117", "SecretsJsonPath": "/config-secrets/gam_prod.json"}, "VcmsStorageSettings": {"ContainerName": "mst-vod-prod-directflow-generated-adi", "CopyContainerName": "directflow-generated-adi", "WriteToBlobStorage": "true"}, "BlobSettings": {"AccountName": "nbaqamediaingest01", "DeltaBackOff": "3", "MaxAttempts": "3", "TenantId": "b40e2cee-d3d7-48a5-8c88-461433c0f520"}, "FreeWheelSettings": {"EnableCallback": "true", "FreeWheelRestEndpoint": "https://api.freewheel.tv/services/v4/transcode/callback", "FreeWheelAccessTokenEndpoint": "https://api.freewheel.tv/auth/token", "SecretsJsonPath": "/config-secrets/freewheel_secrets.json"}, "LiveToVodSettings": {"AccountName": "stmaminputmam1776", "ContainerName": "mst-vod-prod-livetovod", "CopyContainerName": "vcms-generated"}, "NotifierSettings": {"Topics": [{"Name": "ott-pdue2-events-evg001", "Endpoint": "https://ott-pdue2-events-evg001.eastus2-1.eventgrid.azure.net/api/events"}]}, "TranscriptionEngine": {"EnableMediaConvertCron": true, "EnableTranscribeCron": true, "MediaConvertRole": "arn:aws:iam::************:role/MediaConvert", "EnableBackFlow": true, "WorkingBucket": "mst-vod-prod-transcribe"}, "DelayedAssetMonitor": {"AlertThresholdMinutes": 30}, "AssetCopySettings": {"EnableProducer": false, "EnableConsumer": true, "Direction": "migrate"}, "AssetDelivery": {"UseAzure": true, "BucketToContainer": {"mst-vod-prod-normalflow": "normal-flow", "mst-vod-prod-directflow-generated-adi": "directflow-generated-adi", "mst-vod-prod-livetovod": "vcms-generated", "mst-vod-prod-directflow": "direct-flow"}}}