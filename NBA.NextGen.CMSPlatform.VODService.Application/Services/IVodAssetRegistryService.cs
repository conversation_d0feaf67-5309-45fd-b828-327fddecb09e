using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;

namespace NBA.NextGen.CMSPlatform.VODService.Application.Services;


public interface IVodAssetRegistryService
{
    Task CreateAssetEntryAsync(string assetId, string fileName, IngestionFlowTypes flowType);
    Task AddAssetEventAsync(string assetId, AssetEventTypes eventType, DateTime timestampUtc, string message = "", string publicMessage = "");
    public Task<AssetRegistryDetails> GetAssetDetailsByAssetIdAsync(string assetId);
    public Task<List<AssetEventDetails>> GetEventsForAssetIdAsync(string assetId);
}
