// "//-----------------------------------------------------------------------".
// <copyright file="PublishEventCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Events.Commands.PublishEvent
{
    using System.Collections.Generic;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The publish event command.
    /// </summary>
    /// <seealso cref="IRequest{T}" />
    public class PublishEventCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the entity identifier.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the type of entity.
        /// </summary>
        public AquilaEntityType Type { get; set; }

        /// <summary>
        /// Gets or sets the state.
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Gets or sets the calculated state.
        /// </summary>
        public string CalculatedState { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="ChannelInstance"/>s.
        /// </summary>
        public IEnumerable<ChannelInstance> ChannelInstances { get; set; }
    }
}
