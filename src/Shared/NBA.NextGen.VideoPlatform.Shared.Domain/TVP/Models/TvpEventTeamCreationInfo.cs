// "//-----------------------------------------------------------------------".
// <copyright file="TvpEventTeamCreationInfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models
{
    /// <summary>
    /// DTO for TVP Event Team Role creation using TVP Actor.
    /// </summary>
    public class TvpEventTeamCreationInfo
    {
        /// <summary>
        /// Gets or sets the external identifier. This should be the team tricode.
        /// </summary>
        /// <value>
        /// The external identifier.
        /// </value>
        public string ExternalId { get; set; }

        /// <summary>
        /// Gets or sets the type of the role (tipically "home" or "away").
        /// </summary>
        /// <value>
        /// The type of the role.
        /// </value>
        public string RoleType { get; set; }

        /// <summary>
        /// Gets or sets the name.
        /// </summary>
        /// <value>
        /// The name.
        /// </value>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the call letters.
        /// </summary>
        /// <value>
        /// The call letters.
        /// </value>
        public string CallLetters { get; set; }

        /// <summary>
        /// Gets or sets the description.
        /// </summary>
        /// <value>
        /// The description.
        /// </value>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the league.
        /// </summary>
        /// <value>
        /// The league.
        /// </value>
        public string League { get; set; }

        /// <summary>
        /// Gets or sets the location external identifier.
        /// </summary>
        /// <value>
        /// The location external identifier.
        /// </value>
        public string LocationExternalId { get; set; }

        /// <summary>
        /// Gets or sets the conference.
        /// </summary>
        /// <value>
        /// The conference.
        /// </value>
        public string Conference { get; set; }

        /// <summary>
        /// Gets or sets the division.
        /// </summary>
        /// <value>
        /// The division.
        /// </value>
        public string Division { get; set; }
    }
}
