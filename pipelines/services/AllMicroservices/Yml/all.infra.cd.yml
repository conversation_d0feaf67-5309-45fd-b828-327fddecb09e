name: $(Build.DefinitionName)_Release.$(Rev:r)

trigger: none
  
parameters:
  - name: terraform_action
    displayName: Terraform Action
    type: string
    default: apply
    values:
      - apply
  
  - name: AquilaActor
    displayName: Deploy Aquila Actor
    type: boolean
    default: true
  
  - name: AquilaWatchdog
    displayName: Deploy Aquila Watchdog
    type: boolean
    default: true

  - name: APIM
    displayName: Deploy APIM
    type: boolean
    default: false

  - name: Bdd
    displayName: Deploy BDD
    type: boolean
    default: false

  - name: GmsInterpreter
    displayName: Deploy GMS Interpreter
    type: boolean
    default: true

  - name: GmsWatchdog
    displayName: Deploy GMS Watchdog
    type: boolean
    default: true

  - name: Orchestrator
    displayName: Deploy Orchestrator
    type: boolean
    default: true

  - name: PlayoutActor
    displayName: Deploy Playout Actor
    type: boolean
    default: true

  - name: PlayoutService
    displayName: Deploy Playout Service
    type: boolean
    default: true

  - name: PrismaActor 
    displayName: Deploy Prisma Actor
    type: boolean
    default: true

  - name: Scheduler
    displayName: Deploy Scheduler
    type: boolean
    default: true

  - name: ScheduleSerializer
    displayName: Deploy Schedule Serializer
    type: boolean
    default: true

  - name: Simulator
    displayName: Deploy Simulator
    type: boolean
    default: true

  - name: StreamMarker
    displayName: Deploy Stream Marker
    type: boolean
    default: true
  
  - name: TvpActor
    displayName: Deploy Tvp Actor
    type: boolean
    default: true

stages:
  - ${{ if parameters.AquilaActor }}:
    - template: ../../AquilaActor/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 919
        ci_pipeline_name: OTT-IaC-VideoPlatform-AquilaActor-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Aquila_Actor_
  
  - ${{ if parameters.AquilaWatchdog }}:
    - template: ../../AquilaWatchdog/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 914
        ci_pipeline_name: OTT-IaC-VideoPlatform-AquilaWatchdog-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Aquila_Watchdog_

  - ${{ if parameters.Bdd }}:
    - template: ../../Bdd/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 1033
        ci_pipeline_name: OTT-IaC-VideoPlatform-Bdd-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Bdd_

  - ${{ if parameters.APIM }}:
    - template: ../../BackendGateway/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 890
        ci_pipeline_name: OTT-IaC-VideoPlatform-Backend-Gateway-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Backend_

  - ${{ if parameters.GmsInterpreter }}:
    - template: ../../GmsInterpreter/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 923
        ci_pipeline_name: OTT-IaC-VideoPlatform-GmsInterpreter-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Gms_Interpreter_

  - ${{ if parameters.GmsWatchdog }}:
    - template: ../../GmsWatchdog/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 910
        ci_pipeline_name: OTT-IaC-VideoPlatform-GmsWatchdog-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Gms_Watchdog_

  - ${{ if parameters.Orchestrator }}:
    - template: ../../Orchestrator/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 926
        ci_pipeline_name: OTT-IaC-VideoPlatform-Orchestrator-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Orchestrator_

  - ${{ if parameters.PlayoutActor }}:
    - template: ../../PlayoutActor/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 934
        ci_pipeline_name: OTT-IaC-VideoPlatform-PlayoutActor-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Playout_Actor_

  - ${{ if parameters.PlayoutService }}:
    - template: ../../PlayoutService/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 902
        ci_pipeline_name: OTT-IaC-VideoPlatform-PlayoutService-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Playout_Service_

  - ${{ if parameters.PrismaActor }}:
    - template: ../../PrismaActor/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 938
        ci_pipeline_name: OTT-IaC-VideoPlatform-PrismaActor-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Prisma_Actor_

  - ${{ if parameters.Scheduler }}:
    - template: ../../Scheduler/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 946
        ci_pipeline_name: OTT-IaC-VideoPlatform-Scheduler-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Scheduler_

  - ${{ if parameters.ScheduleSerializer }}:
    - template: ../../ScheduleSerializer/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 950
        ci_pipeline_name: OTT-IaC-VideoPlatform-ScheduleSerializer-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Schedule_Serializer_

  - ${{ if parameters.Simulator }}:
    - template: ../../Simulator/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 953
        ci_pipeline_name: OTT-IaC-VideoPlatform-Simulator-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Simulator_

  - ${{ if parameters.StreamMarker }}:
    - template: ../../StreamMarker/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 1029
        ci_pipeline_name: OTT-IaC-VideoPlatform-StreamMarker-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Stream_Marker_

  - ${{ if parameters.TvpActor }}:
    - template: ../../TvpActor/templates/infra.cd.template.yml
      parameters:
        ci_pipeline_id: 964
        ci_pipeline_name: OTT-IaC-VideoPlatform-TvpActor-CI
        terraform_action: ${{ parameters.terraform_action }}
        prefix: Tvp_Actor_